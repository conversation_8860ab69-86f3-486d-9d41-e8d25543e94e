# 基于双层注意力机制的MAPPO多AGV调度优化研究方案（简化版）

## 1. 研究背景与目标

### 1.1 问题定义
多AGV协同调度问题是智能仓储系统中的核心挑战，涉及任务分配、路径规划、碰撞避免和资源优化等多个方面。传统方法在处理大规模动态环境时存在计算复杂度高、协作能力不足等问题。

### 1.2 研究目标
设计一种基于双层注意力机制的MAPPO算法，实现高效的多AGV协同调度，在保证系统性能的同时降低计算复杂度。

## 2. 核心方法论

### 2.1 双层注意力机制设计

#### 2.1.1 第一层：任务分配注意力机制

**输入特征设计**
- AGV状态向量：`s_agv = [x_norm, y_norm, load_norm, target_id, idle_flag]` (5维)
  - `x_norm, y_norm`: 位置坐标归一化到[0,1]
  - `load_norm`: 当前载重/最大载重
  - `target_id`: 当前目标任务ID（归一化）
  - `idle_flag`: 空闲状态标志{0,1}

- 任务状态向量：`s_task = [x_norm, y_norm, weight_norm, status, min_dist]` (5维)
  - `x_norm, y_norm`: 任务位置归一化
  - `weight_norm`: 任务重量归一化{0,1}（5单位→0，10单位→1）
  - `status`: 任务状态{0:未分配, 1:已分配, 2:已完成}
  - `min_dist`: 到最近AGV的归一化距离

**特征嵌入层**
```
h_agv = ReLU(W_agv * s_agv + b_agv)  # 5 → 64维
h_task = ReLU(W_task * s_task + b_task)  # 5 → 64维
```

**注意力计算**
```
Q_i = h_agv_i * W_Q  # AGV i的查询向量
K_j = h_task_j * W_K  # 任务j的键向量
V_j = h_task_j * W_V  # 任务j的值向量

# 计算注意力分数
e_ij = (Q_i · K_j) / sqrt(d_k)
```

**稀疏化优化**
- Top-K距离筛选：只考虑距离最近的K=6个任务
- 距离预筛选：`d_ij = |x_i - x_j| + |y_i - y_j|`
- 选择集合：`T_i^{top-k} = TopK({d_ij}, K=6)`

**约束增强**
```python
# 距离约束
c_distance(i,j) = -λ_d * d_ij / d_max

# 载重约束
c_capacity(i,j) = λ_c if (load_i + weight_j <= capacity_i) else -λ_c

# 可用性约束
c_available(i,j) = 0 if (status_j == available) else -∞

# 增强的注意力分数
e_ij_enhanced = e_ij + c_distance + c_capacity + c_available
```

**最终输出**
```
α_ij = softmax(e_ij_enhanced)  # 任务分配注意力权重
z_i^(1) = Σ_j α_ij * V_j      # 第一层输出
```

#### 2.1.2 第二层：协作感知注意力机制

**协作状态构建**
```python
# 增强的AGV状态
h_agv_enhanced = h_agv + z_i^(1)

# 相对位置编码
r_ij = [sin(Δx/σ), cos(Δx/σ), sin(Δy/σ), cos(Δy/σ)]

# 意图表示（基于第一层注意力权重）
intent_i = Σ_j α_ij * h_task_j

# 协作状态向量
h_collab_i = [h_agv_enhanced; intent_i; r_i_avg]
```

**协作注意力计算**
```python
Q_i^(2) = h_collab_i * W_Q^(2)
K_j^(2) = h_collab_j * W_K^(2)
V_j^(2) = h_collab_j * W_V^(2)

# 协作注意力分数
e_ij^(2) = (Q_i^(2) · K_j^(2)) / sqrt(d_k)
```

**协作约束融合**
```python
# 碰撞风险约束
c_collision(i,j) = -λ_col * exp(-d_ij²/(2σ²))

# 路径冲突约束
c_path(i,j) = -λ_path * PathConflictScore(path_i, path_j)

# 增强的协作注意力
e_ij_collab = e_ij^(2) + c_collision + c_path
β_ij = softmax(e_ij_collab)
z_i^(2) = Σ_j β_ij * V_j^(2)
```

#### 2.1.3 双层注意力融合

**门控融合机制**
```python
# 门控权重计算
gate_i = sigmoid(W_g * [z_i^(1); z_i^(2)] + b_g)

# 融合输出
z_i_final = gate_i ⊙ z_i^(1) + (1 - gate_i) ⊙ z_i^(2)

# 残差连接和层归一化
output_i = LayerNorm(z_i_final + h_agv_enhanced)
```

### 2.2 MAPPO算法融合

#### 2.2.1 策略网络设计

**网络架构**
```python
class PolicyNetwork(nn.Module):
    def __init__(self):
        self.feature_net = nn.Sequential(
            nn.Linear(obs_dim + attention_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU()
        )
        
        # 任务选择头
        self.task_head = nn.Linear(128, num_tasks + 1)  # +1 for wait action
        
        # 运动控制头
        self.motion_head = nn.Linear(128, 5)  # 4方向 + 停止
    
    def forward(self, obs, attention_output):
        input_features = torch.cat([obs, attention_output], dim=-1)
        features = self.feature_net(input_features)
        
        task_logits = self.task_head(features)
        motion_logits = self.motion_head(features)
        
        return task_logits, motion_logits
```

**动作掩码机制**
```python
def apply_action_mask(logits, mask):
    """应用动作掩码，过滤无效动作"""
    masked_logits = logits + (mask - 1) * 1e9
    return F.softmax(masked_logits, dim=-1)

def generate_task_mask(agv_state, task_states):
    """生成任务选择掩码"""
    mask = torch.ones(num_tasks + 1)
    for j, task in enumerate(task_states):
        if task.status != AVAILABLE:
            mask[j] = 0
        if agv_state.load + task.weight > agv_state.capacity:
            mask[j] = 0
    return mask
```

#### 2.2.2 价值网络设计

**中心化价值网络**
```python
class ValueNetwork(nn.Module):
    def __init__(self):
        # 全局状态编码器
        self.global_encoder = nn.Sequential(
            nn.Linear(global_state_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.ReLU()
        )
        
        # 注意力信息编码器
        self.attention_encoder = nn.Sequential(
            nn.Linear(num_agvs * attention_dim, 128),
            nn.ReLU()
        )
        
        # 价值估计头
        self.value_head = nn.Linear(256 + 128, 1)
    
    def forward(self, global_state, attention_outputs):
        global_features = self.global_encoder(global_state)
        attention_features = self.attention_encoder(
            torch.cat(attention_outputs, dim=-1)
        )
        
        combined_features = torch.cat([global_features, attention_features], dim=-1)
        value = self.value_head(combined_features)
        return value
```

#### 2.2.3 训练目标函数

**策略损失函数**
```python
def compute_policy_loss(old_log_probs, new_log_probs, advantages, clip_ratio=0.2):
    """计算PPO策略损失"""
    ratio = torch.exp(new_log_probs - old_log_probs)
    clipped_ratio = torch.clamp(ratio, 1 - clip_ratio, 1 + clip_ratio)
    
    policy_loss = -torch.min(ratio * advantages, clipped_ratio * advantages).mean()
    return policy_loss

def compute_attention_regularization(attention_weights_1, attention_weights_2):
    """计算注意力正则化损失"""
    # L2正则化
    reg_loss = 0.01 * (attention_weights_1.pow(2).sum() + attention_weights_2.pow(2).sum())
    return reg_loss

def compute_temporal_consistency_loss(current_weights, previous_weights):
    """计算时序一致性损失"""
    if previous_weights is None:
        return 0
    consistency_loss = 0.1 * F.mse_loss(current_weights, previous_weights)
    return consistency_loss
```

**总损失函数**
```python
total_loss = (policy_loss + 
              0.5 * value_loss + 
              attention_regularization + 
              temporal_consistency_loss - 
              0.01 * entropy_bonus)
```

### 2.3 环境设计

#### 2.3.1 简化环境配置

**物理环境**
- 地图尺寸：20×8网格
- 货架配置：8个货架，每个3×2网格，2行4列分布
- 通道宽度：1格
- 可通行区域：总面积160格，可通行区域约100格

**智能体配置**
- AGV数量：3个同构AGV
- 载重能力：20单位
- 移动速度：1格/时间步
- 初始位置：随机分布在可通行区域

**任务配置**
- 任务数量：12个运输任务
- 任务重量：5单位或10单位（随机分配）
- 任务位置：分布在货架上（不在通道中）
- 任务优先级：随机分配{1, 2, 3}

#### 2.3.2 状态空间设计

**观察空间**
```python
# 每个AGV的局部观察
observation = {
    'self_state': [x, y, load, target_id, idle_flag],  # 5维
    'visible_tasks': [[x, y, weight, status, priority], ...],  # 最多8个任务
    'nearby_agvs': [[x, y, load, intent], ...],  # 最多2个其他AGV
    'map_info': local_map_patch  # 5×5局部地图
}

# 全局状态（仅用于价值网络）
global_state = {
    'all_agv_states': [...],  # 3×5
    'all_task_states': [...],  # 12×5
    'environment_info': [time_step, completion_rate, ...]  # 环境统计信息
}
```

**动作空间**
```python
action_space = {
    'task_selection': Discrete(13),  # 12个任务 + 1个等待动作
    'motion_control': Discrete(5)    # 上下左右 + 停止
}
```

#### 2.3.3 奖励函数设计

**四个核心奖励组件**

1. **任务完成奖励**
```python
def task_completion_reward(completed_tasks, priorities):
    reward = 0
    for task in completed_tasks:
        base_reward = 10.0
        priority_bonus = task.priority * 2.0
        reward += base_reward + priority_bonus
    return reward
```

2. **移动效率奖励**
```python
def movement_efficiency_reward(agv_actions, distances_moved):
    penalty = 0
    for i, (action, distance) in enumerate(zip(agv_actions, distances_moved)):
        if action != STOP:
            penalty += 0.1 * distance
        else:
            penalty += 0.05  # 轻微的停止惩罚
    return -penalty
```

3. **协作奖励**
```python
def collaboration_reward(agv_states, collision_events, avoidance_events):
    reward = 0

    # 碰撞惩罚
    reward -= 20.0 * len(collision_events)

    # 主动避让奖励
    reward += 5.0 * len(avoidance_events)

    # 负载均衡奖励
    loads = [agv.current_load for agv in agv_states]
    load_variance = np.var(loads)
    reward += 3.0 * (1.0 - load_variance / max_variance)

    return reward
```

4. **载重优化奖励**
```python
def capacity_utilization_reward(agv_states):
    total_utilization = 0
    for agv in agv_states:
        utilization = agv.current_load / agv.max_capacity
        total_utilization += utilization

    avg_utilization = total_utilization / len(agv_states)
    return 2.0 * avg_utilization
```

**总奖励函数**
```python
def compute_total_reward(env_state, actions, stage_weights):
    r1 = task_completion_reward(env_state.completed_tasks, env_state.task_priorities)
    r2 = movement_efficiency_reward(actions, env_state.distances_moved)
    r3 = collaboration_reward(env_state.agv_states, env_state.collisions, env_state.avoidances)
    r4 = capacity_utilization_reward(env_state.agv_states)

    total_reward = (stage_weights['completion'] * r1 +
                   stage_weights['movement'] * r2 +
                   stage_weights['collaboration'] * r3 +
                   stage_weights['capacity'] * r4)

    return total_reward, {'r1': r1, 'r2': r2, 'r3': r3, 'r4': r4}
```

### 2.4 六阶段课程学习策略

#### 2.4.1 阶段配置设计

**阶段参数表**
```python
CURRICULUM_STAGES = {
    1: {
        'num_agvs': 1,
        'num_tasks': 2,
        'map_size': (12, 6),
        'max_episodes': 1000,
        'success_threshold': {'completion_rate': 0.95, 'collision_rate': 0.01},
        'reward_weights': {'completion': 0.6, 'movement': 0.3, 'collaboration': 0.05, 'capacity': 0.05}
    },
    2: {
        'num_agvs': 2,
        'num_tasks': 4,
        'map_size': (16, 6),
        'max_episodes': 1500,
        'success_threshold': {'completion_rate': 0.85, 'collision_rate': 0.03},
        'reward_weights': {'completion': 0.5, 'movement': 0.25, 'collaboration': 0.15, 'capacity': 0.1}
    },
    3: {
        'num_agvs': 2,
        'num_tasks': 6,
        'map_size': (16, 8),
        'max_episodes': 2000,
        'success_threshold': {'completion_rate': 0.80, 'collision_rate': 0.05},
        'reward_weights': {'completion': 0.4, 'movement': 0.2, 'collaboration': 0.25, 'capacity': 0.15}
    },
    4: {
        'num_agvs': 3,
        'num_tasks': 8,
        'map_size': (18, 8),
        'max_episodes': 2500,
        'success_threshold': {'completion_rate': 0.75, 'collision_rate': 0.05},
        'reward_weights': {'completion': 0.35, 'movement': 0.2, 'collaboration': 0.3, 'capacity': 0.15}
    },
    5: {
        'num_agvs': 3,
        'num_tasks': 10,
        'map_size': (20, 8),
        'max_episodes': 3000,
        'success_threshold': {'completion_rate': 0.70, 'collision_rate': 0.05},
        'reward_weights': {'completion': 0.3, 'movement': 0.15, 'collaboration': 0.35, 'capacity': 0.2}
    },
    6: {
        'num_agvs': 3,
        'num_tasks': 12,
        'map_size': (20, 8),
        'max_episodes': 4000,
        'success_threshold': {'completion_rate': 0.65, 'collision_rate': 0.05},
        'reward_weights': {'completion': 0.25, 'movement': 0.15, 'collaboration': 0.4, 'capacity': 0.2}
    }
}
```

#### 2.4.2 阶段转换机制

**性能评估窗口**
```python
class CurriculumManager:
    def __init__(self):
        self.current_stage = 1
        self.evaluation_window = 100  # 评估窗口大小
        self.performance_history = []
        self.consecutive_success_count = 0

    def should_advance_stage(self, recent_performance):
        """判断是否应该进入下一阶段"""
        if len(recent_performance) < self.evaluation_window:
            return False

        # 计算平均性能
        avg_completion = np.mean([p['completion_rate'] for p in recent_performance])
        avg_collision = np.mean([p['collision_rate'] for p in recent_performance])
        std_completion = np.std([p['completion_rate'] for p in recent_performance])

        # 获取当前阶段成功标准
        criteria = CURRICULUM_STAGES[self.current_stage]['success_threshold']

        # 检查核心指标
        meets_completion = avg_completion >= criteria['completion_rate']
        meets_collision = avg_collision <= criteria['collision_rate']
        is_stable = std_completion <= 0.05  # 稳定性要求

        if meets_completion and meets_collision and is_stable:
            self.consecutive_success_count += 1
        else:
            self.consecutive_success_count = 0

        # 需要连续50个评估周期满足条件
        return self.consecutive_success_count >= 50

    def advance_stage(self):
        """进入下一阶段"""
        if self.current_stage < 6:
            self.current_stage += 1
            self.consecutive_success_count = 0
            print(f"Advancing to stage {self.current_stage}")
            return True
        return False
```

#### 2.4.3 动态难度调节

**环境复杂度评估**
```python
def compute_environment_complexity(num_agvs, num_tasks, map_area, obstacles):
    """计算环境复杂度指标"""
    spatial_complexity = (num_agvs * num_tasks) / map_area
    task_complexity = num_tasks / num_agvs
    collaboration_complexity = (num_agvs * (num_agvs - 1)) / (2 * map_area)

    total_complexity = (0.4 * spatial_complexity +
                       0.3 * task_complexity +
                       0.3 * collaboration_complexity)
    return total_complexity

def adjust_difficulty_within_stage(current_performance, target_performance):
    """在阶段内动态调节难度"""
    performance_ratio = current_performance / target_performance

    if performance_ratio > 1.2:  # 表现过好，增加难度
        return {'task_spawn_rate': 1.1, 'agv_speed': 0.9}
    elif performance_ratio < 0.8:  # 表现不佳，降低难度
        return {'task_spawn_rate': 0.9, 'agv_speed': 1.1}
    else:
        return {'task_spawn_rate': 1.0, 'agv_speed': 1.0}
```

### 2.5 训练算法实现

#### 2.5.1 主训练循环

```python
class MAPPOTrainer:
    def __init__(self, config):
        self.config = config
        self.curriculum_manager = CurriculumManager()
        self.dual_attention_model = DualAttentionModel(config)
        self.policy_network = PolicyNetwork(config)
        self.value_network = ValueNetwork(config)
        self.optimizer_policy = torch.optim.Adam(self.policy_network.parameters(), lr=3e-4)
        self.optimizer_value = torch.optim.Adam(self.value_network.parameters(), lr=1e-3)

    def train_episode(self, env):
        """训练一个episode"""
        obs = env.reset()
        episode_data = []

        while not env.done:
            # 计算双层注意力
            attention_outputs = self.dual_attention_model(obs)

            # 生成动作
            actions, log_probs = self.policy_network.sample_actions(obs, attention_outputs)

            # 环境交互
            next_obs, rewards, dones, infos = env.step(actions)

            # 存储经验
            episode_data.append({
                'obs': obs,
                'actions': actions,
                'rewards': rewards,
                'log_probs': log_probs,
                'attention_outputs': attention_outputs
            })

            obs = next_obs

        return episode_data

    def update_networks(self, batch_data):
        """更新网络参数"""
        # 计算优势函数
        advantages = self.compute_advantages(batch_data)

        # 策略网络更新
        policy_loss = self.compute_policy_loss(batch_data, advantages)
        attention_reg = self.compute_attention_regularization(batch_data)
        temporal_loss = self.compute_temporal_consistency_loss(batch_data)

        total_policy_loss = policy_loss + 0.01 * attention_reg + 0.1 * temporal_loss

        self.optimizer_policy.zero_grad()
        total_policy_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.policy_network.parameters(), 0.5)
        self.optimizer_policy.step()

        # 价值网络更新
        value_loss = self.compute_value_loss(batch_data)

        self.optimizer_value.zero_grad()
        value_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.value_network.parameters(), 0.5)
        self.optimizer_value.step()

        return {
            'policy_loss': policy_loss.item(),
            'value_loss': value_loss.item(),
            'attention_reg': attention_reg.item(),
            'temporal_loss': temporal_loss.item()
        }
```

#### 2.5.2 优势函数计算

```python
def compute_advantages(self, episode_data, gamma=0.99, lambda_gae=0.95):
    """使用GAE计算优势函数"""
    rewards = [step['rewards'] for step in episode_data]
    values = [self.value_network(step['obs'], step['attention_outputs'])
              for step in episode_data]

    advantages = []
    gae = 0

    for t in reversed(range(len(rewards))):
        if t == len(rewards) - 1:
            next_value = 0
        else:
            next_value = values[t + 1]

        delta = rewards[t] + gamma * next_value - values[t]
        gae = delta + gamma * lambda_gae * gae
        advantages.insert(0, gae)

    return torch.tensor(advantages, dtype=torch.float32)
```

### 2.6 评估指标体系

#### 2.6.1 核心性能指标

**1. 任务完成率**
```python
def compute_completion_rate(completed_tasks, total_tasks):
    """计算任务完成率"""
    return len(completed_tasks) / total_tasks if total_tasks > 0 else 0

def compute_weighted_completion_rate(completed_tasks, total_tasks, priorities):
    """计算加权任务完成率（考虑优先级）"""
    completed_weight = sum(priorities[task_id] for task_id in completed_tasks)
    total_weight = sum(priorities.values())
    return completed_weight / total_weight if total_weight > 0 else 0
```

**2. AGV载重利用率**
```python
def compute_load_utilization(agv_histories):
    """计算AGV载重利用率"""
    total_utilization = 0

    for agv_history in agv_histories:
        loaded_time = sum(1 for state in agv_history if state['load'] > 0)
        total_time = len(agv_history)
        utilization = loaded_time / total_time if total_time > 0 else 0
        total_utilization += utilization

    return total_utilization / len(agv_histories)

def compute_capacity_efficiency(agv_histories):
    """计算载重容量效率"""
    total_efficiency = 0

    for agv_history in agv_histories:
        avg_load = np.mean([state['load'] for state in agv_history])
        max_capacity = agv_history[0]['max_capacity']
        efficiency = avg_load / max_capacity
        total_efficiency += efficiency

    return total_efficiency / len(agv_histories)
```

**3. 路径效率**
```python
def compute_path_efficiency(agv_paths, optimal_paths):
    """计算路径效率"""
    total_efficiency = 0

    for actual_path, optimal_path in zip(agv_paths, optimal_paths):
        actual_length = len(actual_path)
        optimal_length = len(optimal_path)
        efficiency = optimal_length / actual_length if actual_length > 0 else 0
        total_efficiency += efficiency

    return total_efficiency / len(agv_paths)

def compute_manhattan_distance(pos1, pos2):
    """计算曼哈顿距离"""
    return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
```

**4. 碰撞率**
```python
def compute_collision_rate(collision_events, total_timesteps, num_agvs):
    """计算碰撞率"""
    return len(collision_events) / (total_timesteps * num_agvs) if total_timesteps > 0 else 0

def detect_collisions(agv_positions):
    """检测AGV碰撞"""
    collisions = []
    for i in range(len(agv_positions)):
        for j in range(i + 1, len(agv_positions)):
            if agv_positions[i] == agv_positions[j]:
                collisions.append((i, j))
    return collisions
```

#### 2.6.2 综合性能评估

```python
def compute_comprehensive_score(metrics, weights=None):
    """计算综合性能分数"""
    if weights is None:
        weights = {'completion': 0.3, 'utilization': 0.25, 'efficiency': 0.25, 'safety': 0.2}

    # 归一化指标（碰撞率需要反转）
    normalized_metrics = {
        'completion': metrics['completion_rate'],
        'utilization': metrics['load_utilization'],
        'efficiency': metrics['path_efficiency'],
        'safety': 1.0 - min(metrics['collision_rate'], 1.0)  # 反转并限制在[0,1]
    }

    comprehensive_score = sum(weights[key] * normalized_metrics[key]
                            for key in weights.keys())

    return comprehensive_score, normalized_metrics
```

## 3. 实施计划

### 3.1 开发时间表（12周）

**第1-2周：环境搭建**
- 实现20×8网格仓储环境
- 设计AGV和任务的基础交互逻辑
- 实现碰撞检测和基础路径规划
- 完成Gym环境接口封装
- 实现环境可视化功能

**第3-5周：双层注意力机制实现**
- 实现第一层任务分配注意力机制
- 实现第二层协作感知注意力机制
- 实现注意力融合和稀疏化优化
- 完成注意力机制的单元测试
- 性能优化和调试

**第6-7周：MAPPO算法集成**
- 实现注意力增强的策略网络
- 实现中心化价值网络
- 集成PPO训练算法和损失函数
- 实现经验收集和批处理逻辑
- 完成基础训练循环

**第8-9周：课程学习框架实现**
- 实现6阶段课程学习管理器
- 设计阶段转换和性能评估机制
- 实现动态环境配置和难度调节
- 完成奖励函数的阶段性权重调整
- 集成课程学习到主训练循环

**第10-12周：调试优化和实验验证**
- 训练稳定性调试和超参数调优
- 完整的6阶段训练实验
- 性能指标评估和结果分析
- 代码优化和文档完善
- 实验结果整理和论文撰写准备

### 3.2 技术风险评估

**高风险项**
1. 双层注意力机制的训练稳定性
2. 多智能体环境的收敛性问题
3. 课程学习的阶段转换时机

**中风险项**
1. 计算效率优化
2. 超参数敏感性
3. 环境复杂度平衡

**低风险项**
1. 基础环境实现
2. 标准MAPPO算法集成
3. 性能指标计算

### 3.3 预期成果

**技术贡献**
1. 提出适用于多AGV调度的双层注意力机制
2. 实现注意力机制与MAPPO的有效融合
3. 验证课程学习在复杂多智能体任务中的有效性
4. 提供完整的多AGV调度优化框架

**性能目标**
- 任务完成率：≥65%（相比基准MAPPO提升10%）
- 载重利用率：≥70%（相比基准提升15%）
- 路径效率：≥80%（相比基准提升12%）
- 碰撞率：≤5%（相比基准降低50%）

**学术价值**
- 硕士学位论文的核心技术内容
- 可发表1-2篇会议论文
- 为后续博士研究奠定基础
